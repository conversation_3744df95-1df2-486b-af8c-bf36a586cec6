import 'package:dento_support/core/configs/colors.dart';
import 'package:dento_support/features/clinic/models/clinic.dart';
import 'package:dento_support/features/patients/presentation/bloc/patient_list_bloc.dart';
import 'package:dento_support/features/patients/presentation/widgets/patient_item_widget.dart';
import 'package:dento_support/routers/route_utils.dart';
import 'package:dento_support/routers/router.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// A widget that displays patients in time slot-based layout for clinics with time scheduling enabled.
///
/// This widget supports three different layouts:
/// 1. Single time slot with patients and schedule buttons
/// 2. Multiple time slots with tabbed interface (Slot 1, Slot 2, etc.)
/// 3. Mixed layout with both scheduled patients and available time slots
class TimeSlotPatientsView extends StatefulWidget {
  const TimeSlotPatientsView({
    super.key,
    required this.patients,
    required this.timeRanges,
    required this.selectedDate,
  });

  final List<PatientVisitor> patients;
  final List<TimeRange> timeRanges;
  final DateTime selectedDate;

  @override
  State<TimeSlotPatientsView> createState() => _TimeSlotPatientsViewState();
}

class _TimeSlotPatientsViewState extends State<TimeSlotPatientsView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.timeRanges.length,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // If only one time slot, show single slot view
    if (widget.timeRanges.length == 1) {
      return _buildSingleSlotView();
    }
    
    // If multiple time slots, show tabbed view
    return _buildMultiSlotView();
  }

  Widget _buildSingleSlotView() {
    final timeRange = widget.timeRanges.first;
    final timeSlots = _generateTimeSlotsForSlot(timeRange, 0);

    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        children: [
          ...timeSlots.map((timeSlot) => _buildTimeSlotItem(timeSlot)),
        ],
      ),
    );
  }

  Widget _buildMultiSlotView() {
    return Column(
      children: [
        // Tab bar
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: TabBar(
            controller: _tabController,
            indicator: BoxDecoration(
              color: AppColor.primaryColor,
              borderRadius: BorderRadius.circular(8),
            ),
            labelColor: Colors.white,
            unselectedLabelColor: Colors.grey[600],
            labelStyle: const TextStyle(
              fontFamily: AppFont.inter,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
            unselectedLabelStyle: const TextStyle(
              fontFamily: AppFont.inter,
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
            tabs: widget.timeRanges.asMap().entries.map((entry) {
              return Tab(text: 'Slot ${entry.key + 1}');
            }).toList(),
          ),
        ),
        const SizedBox(height: 16),
        // Tab content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: widget.timeRanges.asMap().entries.map((entry) {
              final slotIndex = entry.key;
              final timeRange = entry.value;
              final timeSlots = _generateTimeSlotsForSlot(timeRange, slotIndex);
              return SingleChildScrollView(
                padding: const EdgeInsets.only(bottom: 20),
                child: Column(
                  children: timeSlots.map((timeSlot) => _buildTimeSlotItem(timeSlot)).toList(),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildTimeSlotItem(TimeSlotData timeSlot) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
      child: Row(
        children: [
          // Time display
          SizedBox(
            width: 80,
            child: Text(
              timeSlot.timeString,
              style: const TextStyle(
                fontFamily: AppFont.inter,
                fontWeight: FontWeight.w500,
                fontSize: 14,
                color: Color(0xFF333333),
              ),
            ),
          ),
          const SizedBox(width: 12),
          // Patient or Schedule button
          Expanded(
            child: timeSlot.patient != null
                ? _buildPatientRow(timeSlot.patient!)
                : _buildScheduleButton(timeSlot.timeString),
          ),
        ],
      ),
    );
  }

  Widget _buildPatientRow(PatientVisitor patient) {
    bool isVisitedTemp;
    if (patient.date.compareTo(DateTime.now()) == -1 ||
        DateTime.now().isAfter(patient.date)) {
      isVisitedTemp = true;
    } else {
      isVisitedTemp = patient.isVisited;
    }

    return InkWell(
      onTap: () => context.push(
        AppPage.patient.toPath,
        extra: PatientExtraNew(patient: patient.patient),
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: BoxDecoration(
          color: const Color(0xFFF8F9FA),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                patient.patient.name,
                style: TextStyle(
                  fontFamily: AppFont.inter,
                  fontWeight: FontWeight.w500,
                  // fontSize: 16,
                  color: isVisitedTemp ? AppColor.textColor : AppColor.red,
                ),
              ),
            ),
            if (patient.patient.totalRemainBill != null &&
                patient.patient.totalRemainBill! > 0)
              Text(
                patient.patient.totalRemainBill.toString(),
                style: const TextStyle(
                  fontFamily: AppFont.inter,
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                  color: AppColor.red,
                ),
              ),
            const SizedBox(width: 8),
            const Icon(
              Icons.arrow_forward_ios_rounded,
              size: 12,
              color: Color(0xFF8F9098),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleButton(String timeString) {
    return InkWell(
      onTap: () {
        // TODO: Implement schedule patient functionality
        // This should open a patient selection dialog or navigate to add patient
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: BoxDecoration(
          color: const Color(0xFFF8F9FA),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Row(
          children: [
            Icon(
              Icons.add,
              color: AppColor.primaryColor,
              size: 16,
            ),
            SizedBox(width: 8),
            Text(
              'Schedule Patient',
              style: TextStyle(
                fontFamily: AppFont.inter,
                fontWeight: FontWeight.w500,
                fontSize: 14,
                color: AppColor.primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<TimeSlotData> _generateTimeSlotsForSlot(TimeRange timeRange, int slotIndex) {
    final startTime = _parseTime(timeRange.start);
    final endTime = _parseTime(timeRange.end);
    final timeSlots = <TimeSlotData>[];

    var currentTime = startTime;
    var patientIndex = 0;

    // Calculate how many patients should be in each slot
    final totalSlots = widget.timeRanges.length;
    final patientsPerSlot = (widget.patients.length / totalSlots).ceil();
    final startPatientIndex = slotIndex * patientsPerSlot;

    while (currentTime.isBefore(endTime)) {
      final timeString = _formatTime(currentTime);

      // Assign patients to time slots based on slot index
      PatientVisitor? patient;
      final actualPatientIndex = startPatientIndex + patientIndex;
      if (actualPatientIndex < widget.patients.length) {
        patient = widget.patients[actualPatientIndex];
      }
      patientIndex++;

      timeSlots.add(TimeSlotData(
        timeString: timeString,
        patient: patient,
      ));

      // Move to next hour
      currentTime = currentTime.add(const Duration(hours: 1));
    }

    return timeSlots;
  }

  DateTime _parseTime(String timeString) {
    final parts = timeString.split(':');
    final hour = int.parse(parts[0]);
    final minute = int.parse(parts[1]);
    
    return DateTime(
      widget.selectedDate.year,
      widget.selectedDate.month,
      widget.selectedDate.day,
      hour,
      minute,
    );
  }

  String _formatTime(DateTime time) {
    final hour = time.hour == 0 ? 12 : (time.hour > 12 ? time.hour - 12 : time.hour);
    final period = time.hour < 12 ? 'AM' : 'PM';
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute $period';
  }
}

class TimeSlotData {
  const TimeSlotData({
    required this.timeString,
    this.patient,
  });

  final String timeString;
  final PatientVisitor? patient;
}
