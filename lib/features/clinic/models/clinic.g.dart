// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'clinic.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TimeRange _$TimeRangeFromJson(Map<String, dynamic> json) => TimeRange(
      start: json['start'] as String,
      end: json['end'] as String,
    );

Map<String, dynamic> _$TimeRangeToJson(TimeRange instance) => <String, dynamic>{
      'start': instance.start,
      'end': instance.end,
    };

Clinic _$ClinicFromJson(Map<String, dynamic> json) => Clinic(
      name: json['name'] as String,
      mobile: json['mobile'] as String,
      dayOff:
          (json['dayOff'] as List<dynamic>).map((e) => e as String).toList(),
      id: (json['id'] as num).toInt(),
      location: json['location'] as String,
      userId: (json['userId'] as num).toInt(),
      scheduleByTime: json['scheduleByTime'] as bool? ?? false,
      timeRanges: (json['timeRanges'] as List<dynamic>?)
              ?.map((e) => TimeRange.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$ClinicToJson(Clinic instance) => <String, dynamic>{
      'name': instance.name,
      'mobile': instance.mobile,
      'dayOff': instance.dayOff,
      'id': instance.id,
      'location': instance.location,
      'userId': instance.userId,
      'scheduleByTime': instance.scheduleByTime,
      'timeRanges': instance.timeRanges,
    };
