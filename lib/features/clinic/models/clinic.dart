import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'clinic.g.dart';

@JsonSerializable()
class TimeRange {
  const TimeRange({
    required this.start,
    required this.end,
  });

  factory TimeRange.fromJson(Map<String, dynamic> json) => _$TimeRangeFromJson(json);

  final String start;
  final String end;

  Map<String, dynamic> toJson() => _$TimeRangeToJson(this);
}

@JsonSerializable()
class Clinic extends Equatable {
  const Clinic({
    required this.name,
    required this.mobile,
    required this.dayOff,
    required this.id,
    required this.location,
    required this.userId,
    this.scheduleByTime = false,
    this.timeRanges = const [],
  });

  factory Clinic.fromJson(Map<String, dynamic> json) => _$ClinicFromJson(json);

  final String name;
  final String mobile;
  final List<String> dayOff;
  final int id;
  final String location;
  final int userId;
  @JsonKey(defaultValue: false)
  final bool scheduleByTime;
  @JsonKey(defaultValue: [])
  final List<TimeRange> timeRanges;

  @override
  List<Object?> get props => [id, name, mobile, dayOff, location, userId, scheduleByTime, timeRanges];

  Map<String, dynamic> toJson() => _$ClinicToJson(this);
}
