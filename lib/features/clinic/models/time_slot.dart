import 'package:flutter/material.dart';

class TimeSlot {
  TimeSlot({
    required this.startTime,
    required this.endTime,
    required this.slotNumber,
  });

  final TimeOfDay? startTime;
  final TimeOfDay? endTime;
  final int slotNumber;

  String get displayText {
    if (startTime == null || endTime == null) {
      return 'Select Time';
    }

    final hour1 = startTime!.hourOfPeriod == 0 ? 12 : startTime!.hourOfPeriod;
    final minute1 = startTime!.minute.toString().padLeft(2, '0');
    final period1 = startTime!.period == DayPeriod.am ? 'AM' : 'PM';
    final start = '$hour1:$minute1 $period1';

    final hour2 = endTime!.hourOfPeriod == 0 ? 12 : endTime!.hourOfPeriod;
    final minute2 = endTime!.minute.toString().padLeft(2, '0');
    final period2 = endTime!.period == DayPeriod.am ? 'AM' : 'PM';
    final end = '$hour2:$minute2 $period2';

    return '$start to $end';
  }

  TimeSlot copyWith({
    TimeOfDay? startTime,
    TimeOfDay? endTime,
    int? slotNumber,
  }) {
    return TimeSlot(
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      slotNumber: slotNumber ?? this.slotNumber,
    );
  }

  bool get hasStartTime => startTime != null;
  bool get hasEndTime => endTime != null;
  bool get isComplete => startTime != null && endTime != null;
}
