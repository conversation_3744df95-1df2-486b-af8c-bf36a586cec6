import 'package:dento_support/features/clinic/models/time_slot.dart';
import 'package:flutter/material.dart';

class ClinicParams {
  const ClinicParams({
    required this.name,
    required this.mobile,
    required this.location,
    required this.dayOff,
    this.scheduleByTime = false,
    this.timeSlots = const [],
  });

  final String name;
  final String mobile;
  final String location;
  final String dayOff;
  final bool scheduleByTime;
  final List<TimeSlot> timeSlots;

  Map<String, dynamic> toJson() {
    final json = {
      'name': name,
      'mobile': mobile,
      'location': location,
      'dayOff': dayOff,
      'scheduleByTime': scheduleByTime,
    };

    if (scheduleByTime && timeSlots.isNotEmpty) {
      json['timeRanges'] = timeSlots
          .where((slot) => slot.isComplete)
          .map((slot) => {
                'start': _formatTimeForApi(slot.startTime!),
                'end': _formatTimeForApi(slot.endTime!),
              })
          .toList();
    }

    return json;
  }

  String _formatTimeForApi(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
}
